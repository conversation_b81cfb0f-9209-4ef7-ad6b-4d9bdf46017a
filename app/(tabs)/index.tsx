import * as ImagePicker from "expo-image-picker";
import * as Location from "expo-location";
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";
import React, { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from "react-native";
import { Button, Card, FAB, Text, useTheme } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import { addPhoto } from "../../store/photoSlice";
import { RootState } from "../../store/store";
import AppBackground from "../components/AppBackground";

// Mock trạng thái chuyến đi
// "in_trip" | "recent_trip" | "no_trip"
type TripState = "in_trip" | "recent_trip" | "no_trip";
const MOCK_STATE: TripState = "no_trip";

export default function HomeScreen() {
  const router = useRouter();
  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);
  const dispatch = useDispatch();
  const [token, setToken] = useState<string | null>(null);
  const trips = useSelector((state: RootState) => state.trip.trips);
  const { colors } = useTheme();
  const { t } = useTranslation();

  useEffect(() => {
    SecureStore.getItemAsync("accessToken").then(setToken);
  }, []);

  const handleCreateTrip = () => {
    if (!isLoggedIn) {
      router.push("/auth/login");
      return;
    }
    router.push("/pages/trip-create-step1");
  };

  const handleCamera = async () => {
    if (!isLoggedIn) {
      router.push("/auth/login");
      return;
    }
    const permission = await ImagePicker.requestCameraPermissionsAsync();
    if (permission.status !== "granted") {
      alert("Bạn cần cấp quyền camera để sử dụng tính năng này!");
      return;
    }
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
      exif: true,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      const photo = result.assets[0];
      // Lấy location hiện tại
      let location = null;
      try {
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status === "granted") {
          location = await Location.getCurrentPositionAsync({});
        }
      } catch {}
      // Lưu vào redux
      dispatch(
        addPhoto({
          path: photo.uri,
          time: photo.exif?.DateTimeOriginal || new Date().toISOString(),
          location: location
            ? {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              }
            : null,
          // tripId: lấy từ redux hoặc context nếu có chuyến đi đang active
        })
      );
    }
  };

  const today = new Date().toISOString().slice(0, 10);
  const todayTrip = trips.find((t) => t.startDate === today);

  return (
    <AppBackground
    <View style={{ flex: 1 }}>
      {/* Card chuyến đi hôm nay (nếu có) */}
      {todayTrip && (
        <Card
          style={{ marginBottom: 5, marginTop: 10, marginHorizontal: 10 }}
          onPress={() =>
            router.push({
              pathname: "/pages/trip-create-step3",
              params: { tripId: todayTrip.id },
            })
          }
        >
          <Card.Title title={t('home.today_trip_title', { name: todayTrip.name })} />
          <Card.Content>
            <Text>{t('home.trip_date', { date: todayTrip.startDate })}</Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="outlined"
              onPress={e => {
                e.stopPropagation && e.stopPropagation();
                router.push({
                  pathname: "/pages/trip-history",
                  params: { tripId: todayTrip.id },
                });
              }}
            >
              {t('home.view_trip_history')}
            </Button>
          </Card.Actions>
        </Card>
      )}
      {/* Box chuyến đi tương lai gần nhất */}
      {(() => {
        // Tìm chuyến đi tương lai gần nhất
        const futureTrips = trips.filter((t) => t.startDate > today);
        if (futureTrips.length === 0) return null;
        // Sắp xếp tăng dần theo ngày
        futureTrips.sort((a, b) => a.startDate.localeCompare(b.startDate));
        const nextTrip = futureTrips[0];
        return (
          <Card
            style={{ marginBottom: 10, marginTop: 10, marginHorizontal: 10 }}
            onPress={() =>
              router.push({
                pathname: "/pages/trip-create-step3",
                params: { tripId: nextTrip.id },
              })
            }
          >
            <Card.Title title={t('home.next_trip_title', { name: nextTrip.name })} />
            <Card.Content>
              <Text>{t('home.trip_date', { date: nextTrip.startDate })}</Text>
            </Card.Content>
          </Card>
        );
      })()}

      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          padding: 16,
        }}
      >
        {/* Card hướng dẫn tạo chuyến đi mới luôn hiển thị */}
        <Card style={{ marginBottom: 24 }}>
          <Card.Title title={t('home.no_trip_title')} />
          <Card.Content>
            <Text>{t('home.no_trip_guide')}</Text>
            <Text>{t('home.no_trip_step1')}</Text>
            <Text>{t('home.no_trip_step2')}</Text>
            <Text>{t('home.no_trip_step3')}</Text>
          </Card.Content>
          <Card.Actions>
            <Button mode="contained" onPress={handleCreateTrip}>
              {t('home.create_trip_button')}
            </Button>
          </Card.Actions>
        </Card>
      </View>

      <FAB
        icon="camera"
        style={[styles.fab, { backgroundColor: colors.primary }]}
        label=""
        onPress={handleCamera}
        size="large"
        color="white"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: "absolute",
    margin: 16,
    bottom: 0,
    alignSelf: "center",
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
  },
});
