import React from "react";
import { FlatList, Image, TouchableOpacity, View } from "react-native";
import ImageViewing from "react-native-image-viewing";
import { Card, Text } from "react-native-paper";
import { useSelector } from "react-redux";

export default function MediaScreen() {
  const photos = useSelector((state: any) => state.photo.photos);
  const trips = useSelector((state: any) => state.trip.trips);
  const [visible, setVisible] = React.useState(false);
  const [currentIndex, setCurrentIndex] = React.useState(0);

  const getTripName = (tripId: string | undefined) => {
    if (!tripId) return undefined;
    const trip = trips.find((t: any) => t.id === tripId);
    return trip ? trip.name : undefined;
  };

  return (
      <AppBackground>
      <FlatList
        data={photos}
        keyExtractor={(item: any) => item.path}
        contentContainerStyle={{ padding: 16 }}
        renderItem={({ item, index }: { item: any; index: number }) => (
          <TouchableOpacity onPress={() => { setCurrentIndex(index); setVisible(true); }}>
            <Card style={{ marginBottom: 24, overflow: 'hidden' }}>
              <Image
                source={{ uri: item.path }}
                style={{ width: '100%', height: 220 }}
                resizeMode="cover"
              />
              <Card.Content>
                <Text variant="titleMedium" style={{ marginTop: 12 }}>
                  {getTripName(item.tripId) || 'Ảnh chuyến đi'}
                </Text>
                <Text style={{ color: '#666', marginTop: 4 }}>{item.time}</Text>
                {item.location && (
                  <Text style={{ color: '#666', marginTop: 2 }}>
                    {item.location.latitude}, {item.location.longitude}
                  </Text>
                )}
              </Card.Content>
            </Card>
          </TouchableOpacity>
        )}
      />
      <ImageViewing
        images={photos.map((item: any) => ({ uri: item.path }))}
        imageIndex={currentIndex}
        visible={visible}
        onRequestClose={() => setVisible(false)}
        FooterComponent={({ imageIndex }) => (
          <View style={{ alignItems: "center", padding: 16 }}>
            <Text style={{ color: "white", fontSize: 18 }}>
              {getTripName(photos[imageIndex]?.tripId) || 'Ảnh chuyến đi'}
            </Text>
            <Text style={{ color: "#ccc", fontSize: 14 }}>{photos[imageIndex]?.time}</Text>
          </View>
        )}
      />
    </>
  );
} 