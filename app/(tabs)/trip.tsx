import { useRouter } from 'expo-router';
import React from 'react';
import { FlatList, View } from 'react-native';
import { Button, List, Text } from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

export default function TripScreen() {
  const trips = useSelector((state: RootState) => state.trip.trips);
  const router = useRouter();
  const today = new Date().toISOString().slice(0, 10);

  return (
    <AppBackground>
      <View style={{ flex: 1, padding: 16 }}>
        <Text variant="titleLarge" style={{ marginBottom: 16 }}>
          Danh sách chuyến đi
        </Text>
        <Button
          mode="contained"
          icon="plus"
          style={{ marginBottom: 16 }}
          onPress={() => router.push('/pages/trip-create-step1')}
        >
          Tạo chuyến đi mới
        </Button>
        <FlatList
          data={trips}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <List.Item
              title={item.name}
              description={`<PERSON><PERSON>y đi: ${item.startDate ? item.startDate.slice(0, 10) : ''}`}
              left={props => <List.Icon {...props} icon="map-marker" />}
              onPress={() => {
                if (item.startDate > today) {
                  router.push({
                    pathname: '/pages/trip-create-step1',
                    params: { tripId: item.id },
                  });
                } else {
                  // Có thể show chi tiết hoặc không cho edit
                }
              }}
            />
          )}
        />
      </View>
    </AppBackground>
  );
}
