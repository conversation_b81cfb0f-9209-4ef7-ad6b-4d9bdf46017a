import { Stack } from "expo-router";
import React from "react";
import { PaperProvider } from "react-native-paper";
import { Provider } from "react-redux";
import { PersistGate } from 'redux-persist/integration/react';
import "../app/i18n/index";
import { persistor, store } from "../store/store";
import { greenTheme } from './theme';

export default function RootLayout() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <PaperProvider theme={greenTheme}>
          <Stack />
        </PaperProvider>
      </PersistGate>
    </Provider>
  );
}
