import DateTimePicker from "@react-native-community/datetimepicker";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import { Platform, View } from "react-native";
import { Button, Text } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { addTrip } from "../../store/tripSlice";

export default function TripCreateStep2() {
  const router = useRouter();
  const { tripName: tripNameParam, tripId } = useLocalSearchParams();
  const trip = useSelector((state: RootState) =>
    tripId ? state.trip.trips.find(t => t.id === tripId) : undefined
  );
  const [tripName, setTripName] = useState(tripNameParam ? String(tripNameParam) : trip?.name || "");
  const [date, setDate] = useState<Date>(
    trip?.startDate ? new Date(trip.startDate) : new Date()
  );
  const [show, setShow] = useState(false);
  const dispatch = useDispatch();
  const { t } = useTranslation();

  useEffect(() => {
    if (trip && trip.startDate) setDate(new Date(trip.startDate));
    if (trip && trip.name) setTripName(trip.name);
  }, [tripId]);

  const handleNext = () => {
    if (date) {
      const newTrip = {
        id: Date.now().toString(),
        name: tripName,
        startDate: date.toISOString().split('T')[0],
        places: [],
      };
      dispatch(addTrip(newTrip));
      router.push({ pathname: "/pages/trip-create-step3", params: { tripId: newTrip.id } });
    }
  };

  return (
    <View style={{ flex: 1, justifyContent: "center", padding: 24 }}>
      <Text variant="titleLarge" style={{ marginBottom: 24 }}>
        {t('trip_create_step2.title')}
      </Text>
      <Button icon="calendar" mode="outlined" onPress={() => setShow(true)} style={{ marginBottom: 12 }}>
        {date.toLocaleDateString()}
      </Button>
      {show && (
        <DateTimePicker
          value={date || new Date()}
          mode="date"
          display={Platform.OS === "ios" ? "inline" : "default"}
          onChange={(_, selectedDate) => {
            setShow(false);
            if (selectedDate) setDate(selectedDate);
          }}
        />
      )}
      <Text style={{ color: "#888", marginBottom: 24 }}>
        {t('trip_create_step2.guide')}
      </Text>
      <Button
        mode="contained"
        onPress={handleNext}
        disabled={!date}
      >
        {t('trip_create_step2.next_button')}
      </Button>
    </View>
  );
} 