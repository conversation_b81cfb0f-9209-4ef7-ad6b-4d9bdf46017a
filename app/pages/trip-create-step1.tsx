import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { Button, Text, TextInput } from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

export default function TripCreateStep1() {
  const router = useRouter();
  const { tripId } = useLocalSearchParams();
  const trip = useSelector((state: RootState) =>
    tripId ? state.trip.trips.find(t => t.id === tripId) : undefined
  );
  const [tripName, setTripName] = useState(trip?.name || '');
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [show, setShow] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (trip && trip.name) setTripName(trip.name);
  }, [tripId]);

  const handleNext = () => {
    router.push({
      pathname: '/pages/trip-create-step2',
      params: { tripName, tripId },
    });
  };

  return (
    <AppBackground>
      <View style={{ flex: 1, justifyContent: 'center', padding: 24 }}>
        <Text variant="titleLarge" style={{ marginBottom: 24 }}>
          {t('trip_create_step1.title')}
        </Text>
        <TextInput
          label={t('trip_create_step1.input_label')}
          value={tripName}
          onChangeText={setTripName}
          style={{ marginBottom: 12 }}
        />
        <Text style={{ color: '#888', marginBottom: 24 }}>
          {t('trip_create_step1.guide')}
        </Text>
        <Button
          mode="contained"
          onPress={handleNext}
          disabled={!tripName.trim()}
        >
          {t('trip_create_step1.next_button')}
        </Button>
      </View>
    </AppBackground>
  );
}
