import { useLocalSearchParams } from "expo-router";
import React from 'react';
import { Dimensions, Image, Text, View } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import AppBackground from '../components/AppBackground';

const { width, height } = Dimensions.get('window');

const getNearestPhoto = (
  place: { latitude: number; longitude: number },
  photos: Array<{ location?: { latitude: number; longitude: number } | null; path: string }>
) => {
  let minDist = Infinity;
  let nearestPhoto = null;
  for (const p of photos) {
    if (p.location) {
      const dLat = p.location.latitude - place.latitude;
      const dLng = p.location.longitude - place.longitude;
      const dist = Math.sqrt(dLat * dLat + dLng * dLng);
      if (dist < minDist) {
        minDist = dist;
        nearestPhoto = p;
      }
    }
  }
  return nearestPhoto;
}

export default function TripHistoryMap() {
  const { tripId } = useLocalSearchParams();
  const trip = useSelector((state: RootState) =>
    state.trip.trips.find(t => t.id === tripId)
  );
  const photos = useSelector((state: RootState) => state.photo.photos);
  const tripPhotos = React.useMemo(
    () => photos.filter(p => p.tripId === tripId),
    [photos, tripId]
  );

  console.log({tripId, trip, tripPhotos});

  if (!trip || !trip.places || trip.places.length === 0) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Không có dữ liệu chuyến đi</Text>
      </View>
    );
  }

  // Lấy region trung tâm
  const region = {
    latitude: trip.places[0].latitude,
    longitude: trip.places[0].longitude,
    latitudeDelta: 0.2,
    longitudeDelta: 0.2,
  };

  return (
    <AppBackground>
      <View style={{ flex: 1 }}>
        <MapView
          style={{ width, height }}
          initialRegion={region}
          region={region}
          provider="google"
        >
          {trip.places.map((place, idx) => {
            const photo = getNearestPhoto(place, tripPhotos);
            console.
            return (
              <Marker
                key={idx}
                coordinate={{
                  latitude: place.latitude,
                  longitude: place.longitude,
                }}
              >
                {photo ? (
                  <Image
                    source={{ uri: photo.path }}
                    style={{
                      width: 70,
                      height: 70,
                      borderRadius: 35,
                      borderWidth: 3,
                      borderColor: '#fff',
                    }}
                  />
                ) : null}
              </Marker>
            );
          })}
        </MapView>
      </View>
    </AppBackground>
  );
}
