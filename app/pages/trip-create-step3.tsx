import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Linking,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-maps';
import {
  Button,
  IconButton,
  List,
  Modal,
  Portal,
  Text,
  TextInput,
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { addPhoto } from '../../store/photoSlice';
import { RootState } from '../../store/store';
import { updateTrip } from '../../store/tripSlice';

// Mock API call
async function searchPlaces(query: string) {
  // Gọi API backend thực tế ở đây
  // Tr<PERSON> về mảng [{ name, address, latitude, longitude }]
  if (!query) return [];
  // Demo mock 5 địa điểm <PERSON>
  const data = [
    {
      name: '<PERSON><PERSON> cổ <PERSON>à <PERSON>ội',
      address: 'Quận Ho<PERSON>, Hà Nội',
      latitude: 21.034952444441426,
      longitude: 105.85042055246173,
    },
    {
      name: 'Văn Miếu - Quốc Tử Giám',
      address: 'Văn Miếu - Quốc Tử Giám, Hà Nội',
      latitude: 21.02902185254678,
      longitude: 105.83554877589249,
    },
    {
      name: 'Lăng Bác',
      address: 'Lăng Bác, Ba Đình, Hà Nội',
      latitude: 21.038261440336086,
      longitude: 105.83467396410137,
    },
    {
      name: 'Chủa Trấn Quốc',
      address: 'Chủa Trấn Quốc, Hà Nội',
      latitude: 21.0490044263297,
      longitude: 105.83679192608956,
    },
    {
      name: 'Phủ Tây Hồ',
      address: 'Phủ Tây Hồ, Hà Nội',
      latitude: 21.056223278010975,
      longitude: 105.81938780409182,
    },
    {
      name: 'Resort World',
      address: 'Resort World, Hà Nội',
      latitude: 20.94712,
      longitude: 105.75638,
    },
  ];
  return data.filter(item =>
    item.name.toLowerCase().includes(query.toLowerCase())
  );
}

type Place = {
  name: string;
  address: string;
  latitude: number;
  longitude: number;
};

export default function TripCreateStep3() {
  const { tripId } = useLocalSearchParams();
  const dispatch = useDispatch();
  const trip = useSelector((state: RootState) =>
    tripId ? state.trip.trips.find(t => t.id === tripId) : undefined
  );
  const [places, setPlaces] = useState(trip?.places || []);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<Place[]>([]);
  const [loading, setLoading] = useState(false);
  const [region, setRegion] = useState({
    latitude: 21.0285,
    longitude: 105.8542,
    latitudeDelta: 0.2,
    longitudeDelta: 0.2,
  });
  const [isFullMap, setIsFullMap] = useState(false);
  const mapRef = useRef<MapView>(null);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const router = useRouter();
  const navigation = useNavigation();
  const trips = useSelector((state: RootState) => state.trip.trips);

  useEffect(() => {
    setPlaces(trip?.places || []);
  }, [tripId, trip?.places]);

  // Debounce search
  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (query.trim()) {
        setLoading(true);
        searchPlaces(query).then(res => {
          setSuggestions(res);
          setLoading(false);
        });
      } else {
        setSuggestions([]);
      }
    }, 300);
    return () => clearTimeout(timeout);
  }, [query]);

  React.useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        alert('Ứng dụng cần quyền truy cập vị trí để sử dụng tính năng này!');
        return;
      }
      let location = await Location.getCurrentPositionAsync({});
      setRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.2,
        longitudeDelta: 0.2,
      });
    })();
  }, []);

  // Fit map to all markers when places change
  React.useEffect(() => {
    if (places.length > 0 && mapRef.current) {
      mapRef.current.fitToCoordinates(
        places.map(p => ({ latitude: p.latitude, longitude: p.longitude })),
        {
          edgePadding: { top: 80, right: 80, bottom: 80, left: 80 },
          animated: true,
        }
      );
    }
  }, [places, isFullMap]);

  const handleSelect = (item: Place) => {
    const newPlaces = [...places, item];
    setPlaces(newPlaces);
    setQuery('');
    setSuggestions([]);
    setRegion({
      latitude: item.latitude,
      longitude: item.longitude,
      latitudeDelta: 0.2,
      longitudeDelta: 0.2,
    });
    if (trip) {
      dispatch(updateTrip({ ...trip, places: newPlaces }));
    }
  };

  const handleOpenDirections = (place: Place) => {
    const lat = place.latitude;
    const lng = place.longitude;
    const label = encodeURIComponent(place.name);

    if (Platform.OS === 'ios') {
      const url = `comgooglemaps://?daddr=${lat},${lng}(${label})&directionsmode=driving`;
      Linking.canOpenURL(url).then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          // Fallback to web
          const webUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
          Linking.openURL(webUrl);
        }
      });
    } else {
      // Android: web URL sẽ tự động mở app nếu đã cài
      const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
      Linking.openURL(url);
    }
  };

  console.log('Current places:', places);

  const handleTakePhoto = async () => {
    const permission = await ImagePicker.requestCameraPermissionsAsync();
    if (permission.status !== 'granted') {
      alert('Bạn cần cấp quyền camera để sử dụng tính năng này!');
      return;
    }
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
      exif: true,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      const photo = result.assets[0];
      let location = null;
      try {
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          location = await Location.getCurrentPositionAsync({});
        }
      } catch {}
      dispatch(
        addPhoto({
          path: photo.uri,
          time: photo.exif?.DateTimeOriginal || new Date().toISOString(),
          location: location
            ? {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              }
            : null,
          tripId: trip?.id,
        })
      );
    }
  };

  const handleSavePlaces = () => {
    if (trip) {
      console.log('Lưu places:', places);
      dispatch(updateTrip({ ...trip, places }));
      alert('Đã cập nhật địa điểm cho chuyến đi!');
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <IconButton icon="home" onPress={() => router.replace('/')} />
      ),
      headerRight: () => <IconButton icon="camera" onPress={handleTakePhoto} />,
    });
  }, [navigation]);

  useEffect(() => {
    const today = new Date().toISOString().slice(0, 10);
    const todayTrip = trips.find(t => t.startDate === today);
    if (!todayTrip) {
      // Nếu không còn trip hôm nay thì quay về Home
      router.replace('/');
    }
    // Nếu vẫn còn trip hôm nay thì giữ nguyên, không cần điều hướng lại step 3
  }, [trips]);

  useEffect(() => {
    console.log('Trip trong store:', trip);
  }, [trip]);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <AppBackground>
        <View style={{ flex: 1, padding: isFullMap ? 0 : 24 }}>
          {!isFullMap && (
            <>
              <Text variant="titleLarge" style={{ marginBottom: 16 }}>
                Thêm các địa điểm sẽ đi qua
              </Text>
              <TextInput
                label="Nhập địa điểm"
                value={query}
                onChangeText={setQuery}
                style={{ marginBottom: 8 }}
              />
              {suggestions.length > 0 && (
                <FlatList
                  data={suggestions}
                  keyExtractor={(_, idx) => idx.toString()}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      onPress={() => handleSelect(item)}
                      style={{
                        padding: 8,
                        backgroundColor: '#f5f5f5',
                        borderBottomWidth: 1,
                        borderColor: '#eee',
                      }}
                    >
                      <Text>{item.name}</Text>
                      <Text style={{ color: '#888', fontSize: 12 }}>
                        {item.address}
                      </Text>
                    </TouchableOpacity>
                  )}
                  style={{
                    maxHeight: 180,
                    marginBottom: 8,
                    borderRadius: 8,
                    overflow: 'hidden',
                  }}
                />
              )}
              <Button
                icon="crosshairs-gps"
                mode="outlined"
                style={{ marginBottom: 8 }}
                onPress={async () => {
                  let { status } =
                    await Location.requestForegroundPermissionsAsync();
                  if (status !== 'granted') {
                    alert(
                      'Ứng dụng cần quyền truy cập vị trí để sử dụng tính năng này!'
                    );
                    return;
                  }
                  let location = await Location.getCurrentPositionAsync({});
                  setRegion({
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude,
                    latitudeDelta: 0.2,
                    longitudeDelta: 0.2,
                  });
                }}
              >
                Lấy vị trí hiện tại
              </Button>
              <Button
                mode="contained"
                disabled
                style={{ marginBottom: 16, display: 'none' }}
              >
                Xác nhận địa điểm
              </Button>
              <Text style={{ color: '#888', marginBottom: 12 }}>
                Mỗi địa điểm bạn chọn sẽ được đánh dấu trên bản đồ bên dưới. Hãy
                nhập và chọn lần lượt các điểm bạn muốn ghé qua!
              </Text>
            </>
          )}
          <View
            style={{
              flex: 1,
              borderRadius: isFullMap ? 0 : 12,
              overflow: 'hidden',
              marginBottom: isFullMap ? 0 : 16,
            }}
          >
            <MapView
              ref={mapRef}
              provider="google"
              style={{ flex: 1 }}
              region={region}
              showsUserLocation={true}
            >
              {places.map((p, idx) => (
                <Marker
                  key={idx}
                  coordinate={{ latitude: p.latitude, longitude: p.longitude }}
                  title={p.name}
                  description={p.address}
                  onPress={() => {
                    setSelectedPlace(p);
                    setModalVisible(true);
                  }}
                />
              ))}
              {places.length > 1 && (
                <Polyline
                  coordinates={places.map(p => ({
                    latitude: p.latitude,
                    longitude: p.longitude,
                  }))}
                  strokeColor="#1976d2"
                  strokeWidth={3}
                />
              )}
            </MapView>
            <IconButton
              icon={isFullMap ? 'fullscreen-exit' : 'fullscreen'}
              size={28}
              style={{
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'white',
                zIndex: 10,
                elevation: 4,
              }}
              onPress={() => setIsFullMap(!isFullMap)}
            />
          </View>
          {!isFullMap && (
            <>
              {places.length > 0 && (
                <View style={{ maxHeight: 90, marginTop: 8 }}>
                  <FlatList
                    data={places}
                    keyExtractor={(_, idx) => idx.toString()}
                    renderItem={({ item, index }) => (
                      <List.Item
                        title={item.name}
                        description={item.address}
                        left={props => (
                          <List.Icon {...props} icon="map-marker" />
                        )}
                        right={props => (
                          <IconButton
                            {...props}
                            icon="delete"
                            onPress={() =>
                              setPlaces(places.filter((_, i) => i !== index))
                            }
                          />
                        )}
                      />
                    )}
                    scrollEnabled
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              )}
            </>
          )}
        </View>
        <Portal>
          <Modal
            visible={modalVisible}
            onDismiss={() => setModalVisible(false)}
            contentContainerStyle={{
              backgroundColor: 'white',
              margin: 24,
              borderRadius: 12,
              padding: 24,
            }}
          >
            <Text variant="titleMedium" style={{ marginBottom: 16 }}>
              {selectedPlace?.name}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                marginBottom: 16,
              }}
            >
              <IconButton
                icon="image-multiple"
                size={32}
                onPress={() => {
                  if (selectedPlace) {
                    setModalVisible(false);
                    const tripId = 'your-trip-id'; // TODO: Lấy tripId thực tế từ props, redux, hoặc context
                    router.push({
                      pathname: '/media',
                      params: { tripId },
                    });
                  }
                }}
              />
              <IconButton
                icon="youtube"
                size={32}
                onPress={() => {
                  if (selectedPlace) {
                    setModalVisible(false);
                    router.push({
                      pathname: '/pages/video',
                      params: {
                        lat: selectedPlace.latitude,
                        lng: selectedPlace.longitude,
                      },
                    });
                  }
                }}
              />
              <IconButton
                icon="directions"
                size={32}
                onPress={() => {
                  if (selectedPlace) {
                    // TODO: Open Google Maps
                    handleOpenDirections(selectedPlace);
                    setModalVisible(false);
                  }
                }}
              />
            </View>
            <Button onPress={() => setModalVisible(false)}>Đóng</Button>
          </Modal>
        </Portal>
      </AppBackground>
    </KeyboardAvoidingView>
  );
}
