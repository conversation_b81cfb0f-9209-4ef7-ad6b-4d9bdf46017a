import { Drawer } from 'expo-router/drawer';
import React from "react";
import { useTheme, IconButton } from "react-native-paper";
import { DrawerActions } from '@react-navigation/native';
import { useNavigation } from 'expo-router';
import { useTranslation } from 'react-i18next';

// Custom header component với Paper IconButton
function CustomDrawerToggle() {
  const navigation = useNavigation();
  const { colors } = useTheme();

  return (
    <IconButton
      icon="menu"
      size={24}
      iconColor={colors.onSurface}
      onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
    />
  );
}

// Component DrawerLayout
export default function DrawerNavigation() {
  const { colors } = useTheme();
  const { t } = useTranslation();

  return (
    <Drawer
      screenOptions={{
        headerShown: true,
        drawerActiveTintColor: '#388e3c',
        headerLeft: () => <CustomDrawerToggle />,
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.onSurface,
      }}
    >
      {/* Các screen hiển thị trong Drawer */}
      <Drawer.Screen name="(tabs)" options={{ drawerLabel: t('navigation.home'), title: t('navigation.home') }} />
      <Drawer.Screen name="pages/trip-history" options={{ drawerLabel: t('navigation.trip_history'), title: t('navigation.trip_history') }} />
      <Drawer.Screen name="pages/video" options={{ drawerLabel: t('navigation.media'), title: t('navigation.media') }} />

      {/* Ẩn các screen không muốn hiển thị trong Drawer */}
      <Drawer.Screen name="auth/login" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="auth/register" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="auth/verify-email" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="pages/trip-create-step1" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="pages/trip-create-step2" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="pages/trip-create-step3" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="components/AppBackground" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="components/DrawerNavigation" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="features/authSlice" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="i18n/index" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="services/api" options={{ drawerItemStyle: { display: 'none' } }} />
      <Drawer.Screen name="theme" options={{ drawerItemStyle: { display: 'none' } }} />
    </Drawer>
  );
}